---
title: "<PERSON><PERSON> Console"
---

Envoy Gateway provides a built-in web-based admin console that offers a comprehensive interface for monitoring, debugging, and managing your Envoy Gateway deployment. The admin console provides real-time visibility into the control plane status, configuration state, performance metrics, and debugging capabilities.

## Prerequisites

{{< boilerplate o11y_prerequisites >}}

## Overview

The admin console is automatically enabled and provides the following key features:

- **Dashboard**: Overview of system status and quick access to all features
- **Server Information**: Detailed runtime information about Envoy Gateway components
- **Configuration Dump**: Real-time view of Gateway API resources and their status
- **Statistics**: Control plane metrics in Prometheus format
- **Performance Profiling**: pprof endpoints for debugging and performance analysis

## Accessing the Admin Console

By default, the admin console is available on `localhost:19000`. You can access it using port forwarding:

```shell
kubectl port-forward -n envoy-gateway-system deployment/envoy-gateway 19000:19000
```

Then open your browser and navigate to:

```
http://localhost:19000
```

## Configuration

The admin console can be configured through the `EnvoyGateway` configuration resource:

### Basic Configuration

```yaml
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
metadata:
  name: envoy-gateway-config
  namespace: envoy-gateway-system
spec:
  admin:
    # Admin server address configuration
    address:
      # Host to bind the admin server (default: 127.0.0.1)
      host: "127.0.0.1"
      # Port for the admin server (default: 19000)
      port: 19000
    
    # Enable pprof endpoints for debugging (default: false)
    enablePprof: false
    
    # Enable config dump in logs (default: false)
    enableDumpConfig: false
```

### Development Configuration

For development environments, you may want to enable additional debugging features:

```yaml
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
metadata:
  name: envoy-gateway-dev
  namespace: envoy-gateway-system
spec:
  admin:
    address:
      # Allow external access (use with caution)
      host: "0.0.0.0"
      port: 19000
    
    # Enable pprof for performance debugging
    enablePprof: true
    
    # Enable config dump for troubleshooting
    enableDumpConfig: true
```

### Production Configuration

For production environments, use more restrictive settings:

```yaml
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
metadata:
  name: envoy-gateway-prod
  namespace: envoy-gateway-system
spec:
  admin:
    address:
      # Localhost only for security
      host: "127.0.0.1"
      port: 19000
    
    # Disable pprof in production
    enablePprof: false
    
    # Disable config dump in logs
    enableDumpConfig: false
```

## Features

### Dashboard

The main dashboard provides:

- **System Information**: Version, uptime, and platform details
- **Component Status**: Real-time status of all Envoy Gateway components
- **Quick Navigation**: Easy access to all admin console features
- **Auto-refresh**: Automatic updates every 30 seconds

### Server Information

The server info page displays:

- **Component Health**: Status of Provider Service, GatewayAPI Translator, xDS Translator, and Infrastructure Manager
- **Runtime Details**: Version information, uptime, and system metrics
- **Configuration**: Current EnvoyGateway configuration settings

### Configuration Dump

The config dump feature provides:

- **Resource Explorer**: Browse all Gateway API resources (Gateways, HTTPRoutes, etc.)
- **Search Functionality**: Find resources by name or namespace
- **Real-time Updates**: Live view of configuration changes
- **JSON Export**: Download complete configuration as JSON

### Statistics

The statistics page offers:

- **Prometheus Metrics**: All control plane metrics in Prometheus format
- **Metrics Categories**: Organized view of different metric types:
  - Watching Components (event-driven architecture metrics)
  - Status Updater (resource status update metrics)
  - xDS Server (proxy configuration delivery metrics)
  - Infrastructure Manager (Kubernetes resource operation metrics)
  - Wasm (WebAssembly extension metrics)
  - Topology Injector (node topology injection metrics)

Access metrics directly via: `http://localhost:19000/api/metrics`

### Performance Profiling

When `enablePprof` is set to `true`, the profiling page provides:

- **CPU Profile**: Analyze CPU usage patterns
- **Memory Heap**: Monitor memory allocation and identify leaks
- **Goroutines**: Debug concurrency issues and goroutine leaks
- **Mutex/Block**: Find contention points in the application

{{% alert title="Security Warning" color="warning" %}}
Only enable pprof endpoints in development or debugging scenarios. These endpoints can expose sensitive information and should not be enabled in production environments.
{{% /alert %}}

## API Endpoints

The admin console also exposes REST API endpoints for programmatic access:

| Endpoint | Description |
|----------|-------------|
| `/api/info` | Basic system information |
| `/api/server_info` | Detailed server and component status |
| `/api/config_dump` | Configuration dump (summary view) |
| `/api/config_dump?resource=all` | Complete configuration dump (JSON) |
| `/api/metrics` | Prometheus metrics |

## Security Considerations

{{% alert title="Important Security Notes" color="warning" %}}
The admin console does not have built-in authentication or authorization mechanisms. Access control must be implemented at the network level.
{{% /alert %}}

- **Network Access**: By default, the admin console binds to `127.0.0.1` (localhost only)
- **No Authentication**: The console does not require authentication - anyone with network access can view all information
- **Sensitive Information**: The console exposes configuration details, metrics, and potentially sensitive system information
- **Production Use**: Avoid exposing the admin console to external networks in production environments
- **pprof Endpoints**: Only enable pprof in development environments as they can expose sensitive runtime information
- **Access Control**: Implement network-level restrictions using:
  - Kubernetes Network Policies
  - Service mesh security policies
  - Firewall rules
  - VPN or bastion host access

### Example Network Policy

To restrict access to the admin console, you can use a Kubernetes Network Policy:

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: envoy-gateway-admin-access
  namespace: envoy-gateway-system
spec:
  podSelector:
    matchLabels:
      control-plane: envoy-gateway
  policyTypes:
  - Ingress
  ingress:
  - from:
    # Only allow access from specific namespaces or pods
    - namespaceSelector:
        matchLabels:
          name: monitoring
    # Only allow access to admin port
    ports:
    - protocol: TCP
      port: 19000
```

## Troubleshooting

### Console Not Accessible

If you cannot access the admin console:

1. Verify the Envoy Gateway deployment is running:

   ```shell
   kubectl get pods -n envoy-gateway-system
   ```

2. Check the admin server configuration:

   ```shell
   kubectl get envoygateway -n envoy-gateway-system -o yaml
   ```

3. Verify port forwarding is working:

   ```shell
   kubectl port-forward -n envoy-gateway-system deployment/envoy-gateway 19000:19000
   ```

### Performance Issues

If the console is slow or unresponsive:

1. Check system resources and load
2. Review the metrics for any anomalies
3. Consider disabling auto-refresh for large configurations
4. Use the JSON API endpoints for programmatic access instead of the web interface

## Common Use Cases

### Debugging Configuration Issues

Use the config dump feature to troubleshoot Gateway API resource issues:

1. Navigate to the Config Dump page
2. Search for specific resources by name or namespace
3. Check resource status and validation errors
4. Export complete configuration for offline analysis

### Monitoring Control Plane Health

Monitor the health of Envoy Gateway components:

1. Check the Server Info page for component status
2. Review uptime and version information
3. Monitor metrics for performance issues
4. Set up alerts based on component health

### Performance Analysis

Use the profiling features for performance troubleshooting:

1. Enable pprof in development environments
2. Collect CPU and memory profiles during high load
3. Analyze goroutine usage and potential leaks
4. Identify performance bottlenecks

## Integration with Monitoring

The admin console metrics endpoint can be integrated with monitoring systems:

```yaml
# Prometheus scrape configuration
- job_name: 'envoy-gateway-admin'
  static_configs:
    - targets: ['envoy-gateway.envoy-gateway-system.svc.cluster.local:19000']
  metrics_path: '/api/metrics'
```

### ServiceMonitor for Prometheus Operator

```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: envoy-gateway-admin
  namespace: envoy-gateway-system
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: envoy-gateway
  endpoints:
  - port: admin
    path: /api/metrics
    interval: 30s
```

For more information about Envoy Gateway metrics, see the [Gateway Exported Metrics](./gateway-exported-metrics) documentation.
